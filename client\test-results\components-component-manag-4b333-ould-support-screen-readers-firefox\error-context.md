# Page snapshot

```yaml
- button "Open Next.js Dev Tools":
  - img
- button "Open issues overlay": 1 Issue
- navigation:
  - button "previous" [disabled]:
    - img "previous"
  - text: 1/1
  - button "next" [disabled]:
    - img "next"
- img
- img
- text: Next.js 15.4.1 Webpack
- img
- dialog "Build Error":
  - text: Build Error
  - button "Copy Stack Trace":
    - img
  - link "Go to related documentation":
    - /url: https://nextjs.org/docs/messages/module-not-found
    - img
  - link "Learn more about enabling Node.js inspector for server code with Chrome DevTools":
    - /url: https://nextjs.org/docs/app/building-your-application/configuring/debugging#server-side-code
    - img
  - paragraph: "Module not found: Can't resolve 'hooks/useAuth'"
  - img
  - text: ./src/app/dashboard/page.tsx (7:1)
  - button "Open in editor":
    - img
  - text: "Module not found: Can't resolve 'hooks/useAuth' 5 | import { UserProfile } from '@/components/auth/UserProfile' 6 | import { DashboardLayout } from '@/components/layout/DashboardLayout' > 7 | import { useAuth } from 'hooks/useAuth' | ^ 8 | 9 | function DashboardContent() { 10 | const { user, isAdmin } = useAuth()"
  - link "https://nextjs.org/docs/messages/module-not-found":
    - /url: https://nextjs.org/docs/messages/module-not-found
- alert
```